<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能重构验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>登录功能重构验证</h1>
    <p>验证重构后的登录功能是否正常工作</p>
    
    <div class="test-container">
        <h2>环境检查</h2>
        <button onclick="checkEnvironment()">检查环境</button>
        <div id="environmentResult"></div>
    </div>

    <div class="test-container">
        <h2>函数可用性测试</h2>
        <button onclick="testFunctions()">测试函数</button>
        <div id="functionResult"></div>
    </div>

    <div class="test-container">
        <h2>登录功能测试</h2>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult"></div>
    </div>

    <div class="test-container">
        <h2>Cookie管理测试</h2>
        <button onclick="testCookies()">测试Cookie</button>
        <div id="cookieResult"></div>
    </div>

    <!-- 引入config.js -->
    <script src="config.js"></script>
    
    <script>
        // 显示结果的辅助函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            
            element.innerHTML = `
                <div class="test-result ${type}">
                    [${timestamp}] ${message}
                </div>
            ` + element.innerHTML;
        }

        // 环境检查
        function checkEnvironment() {
            showResult('environmentResult', '开始检查环境...', 'info');
            
            const requiredFunctions = [
                'loginUser', 'getTokenFromCookies', 'updateAuthToken', 
                'getCookie', 'deleteCookie', 'validateToken'
            ];
            
            const results = [];
            const missing = [];
            
            requiredFunctions.forEach(func => {
                if (typeof window[func] === 'function') {
                    results.push(`✅ ${func}: 已加载`);
                } else {
                    results.push(`❌ ${func}: 未找到`);
                    missing.push(func);
                }
            });
            
            const summary = missing.length === 0 
                ? '✅ 环境检查通过，所有必需函数已加载'
                : `❌ 环境检查失败，缺少函数: ${missing.join(', ')}`;
            
            showResult('environmentResult', 
                `${summary}\n\n详细结果:\n${results.join('\n')}`, 
                missing.length === 0 ? 'success' : 'error'
            );
        }

        // 测试函数可用性
        function testFunctions() {
            showResult('functionResult', '测试函数可用性...', 'info');
            
            const tests = [];
            
            // 测试getCookie
            try {
                const testResult = getCookie('test-cookie');
                tests.push('✅ getCookie: 可调用');
            } catch (error) {
                tests.push(`❌ getCookie: ${error.message}`);
            }
            
            // 测试deleteCookie
            try {
                deleteCookie('test-cookie');
                tests.push('✅ deleteCookie: 可调用');
            } catch (error) {
                tests.push(`❌ deleteCookie: ${error.message}`);
            }
            
            // 测试getTokenFromCookies
            try {
                const token = getTokenFromCookies();
                tests.push('✅ getTokenFromCookies: 可调用');
            } catch (error) {
                tests.push(`❌ getTokenFromCookies: ${error.message}`);
            }
            
            showResult('functionResult', tests.join('\n'), 'success');
        }

        // 测试登录功能
        async function testLogin() {
            showResult('loginResult', '测试登录功能...', 'info');
            
            try {
                if (typeof loginUser !== 'function') {
                    throw new Error('loginUser函数未找到');
                }
                
                const result = await loginUser('bydq_admin', 'Aa123456', '', 1);
                
                if (result.success) {
                    showResult('loginResult', 
                        `✅ 登录测试成功!\n` +
                        `Token长度: ${result.token.length}\n` +
                        `消息: ${result.message}`, 
                        'success'
                    );
                } else {
                    showResult('loginResult', 
                        `❌ 登录测试失败: ${result.message}`, 
                        'error'
                    );
                }
            } catch (error) {
                showResult('loginResult', 
                    `❌ 登录测试异常: ${error.message}`, 
                    'error'
                );
            }
        }

        // 测试Cookie管理
        async function testCookies() {
            showResult('cookieResult', '测试Cookie管理...', 'info');
            
            try {
                // 先登录获取Token
                const loginResult = await loginUser('bydq_admin', 'Aa123456', '', 1);
                
                if (!loginResult.success) {
                    throw new Error('无法获取有效Token进行Cookie测试');
                }
                
                // 测试updateAuthToken
                updateAuthToken(loginResult.token);
                
                // 测试getTokenFromCookies
                const retrievedToken = getTokenFromCookies();
                
                const tests = [
                    `✅ updateAuthToken: 执行成功`,
                    `✅ getTokenFromCookies: 获取到Token`,
                    `✅ Token匹配: ${retrievedToken === loginResult.token ? '是' : '否'}`,
                    `✅ Token长度: ${retrievedToken ? retrievedToken.length : 0}`
                ];
                
                showResult('cookieResult', tests.join('\n'), 'success');
                
            } catch (error) {
                showResult('cookieResult', 
                    `❌ Cookie测试异常: ${error.message}`, 
                    'error'
                );
            }
        }

        // 页面加载完成后自动检查环境
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkEnvironment, 500);
        });
    </script>
</body>
</html>
